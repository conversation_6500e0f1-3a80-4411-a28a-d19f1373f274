<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8.00"
	Name="BaseClasses"
	ProjectGUID="{E8A3F6FA-AE1C-4C8E-A0B6-9C8480324EAA}"
	RootNamespace="BaseClasses"
	Keyword="Win32Proj"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
		<Platform
			Name="x64"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="Debug"
			IntermediateDirectory="Debug"
			ConfigurationType="4"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="."
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalDependencies="strmiids.lib"
				OutputFile="$(OutDir)\strmbasd.lib"
				AdditionalLibraryDirectories=""
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="Release"
			IntermediateDirectory="Release"
			ConfigurationType="4"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="."
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB;"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalDependencies="strmiids.lib"
				OutputFile="$(OutDir)\strmbase.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug_MBCS|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="."
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalDependencies="strmiids.lib"
				OutputFile="$(OutDir)\strmbasd.lib"
				AdditionalLibraryDirectories=""
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release_MBCS|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="."
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB;"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalDependencies="strmiids.lib"
				OutputFile="$(OutDir)\strmbase.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="."
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalDependencies="strmiids.lib"
				OutputFile="$(OutDir)\strmbasd.lib"
				AdditionalLibraryDirectories=""
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="."
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB;"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalDependencies="strmiids.lib"
				OutputFile="$(OutDir)\strmbase.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug_MBCS|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="."
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalDependencies="strmiids.lib"
				OutputFile="$(OutDir)\strmbasd.lib"
				AdditionalLibraryDirectories=""
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release_MBCS|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				AdditionalIncludeDirectories="."
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB;"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalDependencies="strmiids.lib"
				OutputFile="$(OutDir)\strmbase.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath=".\amextra.cpp"
				>
			</File>
			<File
				RelativePath=".\amfilter.cpp"
				>
			</File>
			<File
				RelativePath=".\amvideo.cpp"
				>
			</File>
			<File
				RelativePath=".\arithutil.cpp"
				>
			</File>
			<File
				RelativePath=".\combase.cpp"
				>
			</File>
			<File
				RelativePath=".\cprop.cpp"
				>
			</File>
			<File
				RelativePath=".\ctlutil.cpp"
				>
			</File>
			<File
				RelativePath=".\ddmm.cpp"
				>
			</File>
			<File
				RelativePath=".\dllentry.cpp"
				>
			</File>
			<File
				RelativePath=".\dllsetup.cpp"
				>
			</File>
			<File
				RelativePath=".\mtype.cpp"
				>
			</File>
			<File
				RelativePath=".\outputq.cpp"
				>
			</File>
			<File
				RelativePath=".\perflog.cpp"
				>
			</File>
			<File
				RelativePath=".\pstream.cpp"
				>
			</File>
			<File
				RelativePath=".\pullpin.cpp"
				>
			</File>
			<File
				RelativePath=".\refclock.cpp"
				>
			</File>
			<File
				RelativePath=".\renbase.cpp"
				>
			</File>
			<File
				RelativePath=".\schedule.cpp"
				>
			</File>
			<File
				RelativePath=".\seekpt.cpp"
				>
			</File>
			<File
				RelativePath=".\source.cpp"
				>
			</File>
			<File
				RelativePath=".\strmctl.cpp"
				>
			</File>
			<File
				RelativePath=".\sysclock.cpp"
				>
			</File>
			<File
				RelativePath=".\transfrm.cpp"
				>
			</File>
			<File
				RelativePath=".\transip.cpp"
				>
			</File>
			<File
				RelativePath=".\videoctl.cpp"
				>
			</File>
			<File
				RelativePath=".\vtrans.cpp"
				>
			</File>
			<File
				RelativePath=".\winctrl.cpp"
				>
			</File>
			<File
				RelativePath=".\winutil.cpp"
				>
			</File>
			<File
				RelativePath=".\wxdebug.cpp"
				>
			</File>
			<File
				RelativePath=".\wxlist.cpp"
				>
			</File>
			<File
				RelativePath=".\wxutil.cpp"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath=".\amextra.h"
				>
			</File>
			<File
				RelativePath=".\amfilter.h"
				>
			</File>
			<File
				RelativePath=".\cache.h"
				>
			</File>
			<File
				RelativePath=".\combase.h"
				>
			</File>
			<File
				RelativePath=".\cprop.h"
				>
			</File>
			<File
				RelativePath=".\ctlutil.h"
				>
			</File>
			<File
				RelativePath=".\ddmm.h"
				>
			</File>
			<File
				RelativePath=".\dllsetup.h"
				>
			</File>
			<File
				RelativePath=".\dxmperf.h"
				>
			</File>
			<File
				RelativePath=".\fourcc.h"
				>
			</File>
			<File
				RelativePath=".\measure.h"
				>
			</File>
			<File
				RelativePath=".\msgthrd.h"
				>
			</File>
			<File
				RelativePath=".\mtype.h"
				>
			</File>
			<File
				RelativePath=".\outputq.h"
				>
			</File>
			<File
				RelativePath=".\perflog.h"
				>
			</File>
			<File
				RelativePath=".\perfstruct.h"
				>
			</File>
			<File
				RelativePath=".\pstream.h"
				>
			</File>
			<File
				RelativePath=".\pullpin.h"
				>
			</File>
			<File
				RelativePath=".\refclock.h"
				>
			</File>
			<File
				RelativePath=".\reftime.h"
				>
			</File>
			<File
				RelativePath=".\renbase.h"
				>
			</File>
			<File
				RelativePath=".\schedule.h"
				>
			</File>
			<File
				RelativePath=".\seekpt.h"
				>
			</File>
			<File
				RelativePath=".\source.h"
				>
			</File>
			<File
				RelativePath=".\streams.h"
				>
			</File>
			<File
				RelativePath=".\strmctl.h"
				>
			</File>
			<File
				RelativePath=".\sysclock.h"
				>
			</File>
			<File
				RelativePath=".\transfrm.h"
				>
			</File>
			<File
				RelativePath=".\transip.h"
				>
			</File>
			<File
				RelativePath=".\videoctl.h"
				>
			</File>
			<File
				RelativePath=".\vtrans.h"
				>
			</File>
			<File
				RelativePath=".\winctrl.h"
				>
			</File>
			<File
				RelativePath=".\winutil.h"
				>
			</File>
			<File
				RelativePath=".\wxdebug.h"
				>
			</File>
			<File
				RelativePath=".\wxlist.h"
				>
			</File>
			<File
				RelativePath=".\wxutil.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
