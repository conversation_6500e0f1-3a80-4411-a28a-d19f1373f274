# --- obsensor ---
if(NOT HAVE_OBSENSOR)
  if(WIN32)
    check_include_file(mfapi.h HAVE_MFAPI)
    check_include_file(vidcap.h HAVE_VIDCAP)
    if(HAVE_MFAPI AND HAVE_VIDCAP)
      set(HAVE_OBSENSOR TRUE)
      set(HAVE_OBSENSOR_MSMF TRUE)
      ocv_add_external_target(obsensor "" "" "HAVE_OBSENSOR;HAVE_OBSENSOR_MSMF")
    else()
      set(HAVE_OBSENSOR OFF)
      set(HAVE_OBSENSOR_MSMF OFF)
      if(NOT HAVE_MFAPI)
        MESSAGE(STATUS "Could not find mfapi.h. Turning HAVE_OBSENSOR OFF")
      endif()
      if(NOT HAVE_VIDCAP)
        MESSAGE(STATUS "Could not find vidcap.h. Turning HAVE_OBSENSOR OFF")
      endif()
    endif()
  elseif(UNIX)
    check_include_file(linux/videodev2.h HAVE_CAMV4L2)
    if(HAVE_CAMV4L2)
      set(HAVE_OBSENSOR TRUE)
      set(HAVE_OBSENSOR_V4L2 TRUE)
      ocv_add_external_target(obsensor "" "" "HAVE_OBSENSOR;HAVE_OBSENSOR_V4L2")
    endif()
  endif()
endif()
