#!/usr/bin/env python3
"""
Test script for GStreamer IR streaming functionality
"""

import sys
import cv2
import numpy as np
import gi
gi.require_version('Gst', '1.0')
from gi.repository import Gst
import time

def test_gstreamer_pipeline():
    """Test the GStreamer pipeline independently"""
    print("Testing GStreamer pipeline...")
    
    # Initialize GStreamer
    Gst.init(None)
    
    # Create the same pipeline as in display.py
    pipeline_str = (
        "appsrc name=src is-live=true do-timestamp=true ! "
        "videoconvert ! "
        "video/x-raw,format=I420 ! "
        "x264enc tune=zerolatency bitrate=2000 speed-preset=ultrafast key-int-max=30 ! "
        "rtph264pay config-interval=1 pt=96 ! "
        "udpsink host=127.0.0.1 port=5000 sync=false"
    )
    
    try:
        pipeline = Gst.parse_launch(pipeline_str)
        appsrc = pipeline.get_by_name("src")
        
        # Configure appsrc
        caps_str = "video/x-raw,format=GRAY8,width=1920,height=1080,framerate=30/1"
        caps = Gst.Caps.from_string(caps_str)
        appsrc.set_property("caps", caps)
        appsrc.set_property("stream-type", 0)
        appsrc.set_property("format", Gst.Format.TIME)
        appsrc.set_property("is-live", True)
        appsrc.set_property("do-timestamp", True)
        
        # Start pipeline
        ret = pipeline.set_state(Gst.State.PLAYING)
        if ret == Gst.StateChangeReturn.FAILURE:
            print("Failed to start pipeline")
            return False
        
        print("Pipeline started successfully!")
        print("VLC command: vlc udp://@:5000")
        print("Sending test frames for 30 seconds...")
        
        # Send test frames
        frame_count = 0
        start_time = time.time()
        
        while time.time() - start_time < 30:  # Run for 30 seconds
            # Create a test frame with moving pattern
            test_frame = np.zeros((1080, 1920), dtype=np.uint8)
            
            # Create a moving vertical bar
            bar_pos = int((frame_count % 100) * 19.2)  # Move across screen
            test_frame[:, bar_pos:bar_pos+50] = 255
            
            # Add frame counter text pattern
            cv2.putText(test_frame, f"Frame {frame_count}", (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 2, 255, 3)
            
            # Convert to bytes
            frame_data = test_frame.tobytes()
            
            # Create buffer
            buffer = Gst.Buffer.new_allocate(None, len(frame_data), None)
            buffer.fill(0, frame_data)
            
            # Set timestamp
            timestamp = pipeline.get_clock().get_time() - pipeline.get_base_time()
            buffer.pts = timestamp
            buffer.dts = timestamp
            
            # Push buffer
            ret = appsrc.emit("push-buffer", buffer)
            if ret != Gst.FlowReturn.OK:
                print(f"Error pushing buffer: {ret}")
            else:
                if frame_count % 30 == 0:  # Print every 30 frames (1 second at 30fps)
                    print(f"Sent frame {frame_count}")
            
            frame_count += 1
            time.sleep(1/30)  # 30 FPS
        
        # Cleanup
        pipeline.set_state(Gst.State.NULL)
        print("Test completed!")
        return True
        
    except Exception as e:
        print(f"Error in test: {e}")
        return False

def check_gstreamer_elements():
    """Check if required GStreamer elements are available"""
    print("Checking GStreamer elements...")
    
    Gst.init(None)
    
    required_elements = [
        'appsrc', 'videoconvert', 'x264enc', 'rtph264pay', 'udpsink'
    ]
    
    missing_elements = []
    for element in required_elements:
        factory = Gst.ElementFactory.find(element)
        if factory is None:
            missing_elements.append(element)
        else:
            print(f"✓ {element} - available")
    
    if missing_elements:
        print(f"✗ Missing elements: {missing_elements}")
        print("Install missing GStreamer plugins:")
        print("sudo apt-get install gstreamer1.0-plugins-base gstreamer1.0-plugins-good gstreamer1.0-plugins-bad gstreamer1.0-plugins-ugly gstreamer1.0-libav")
        return False
    else:
        print("All required GStreamer elements are available!")
        return True

if __name__ == "__main__":
    print("GStreamer IR Streaming Test")
    print("=" * 40)
    
    # Check elements first
    if not check_gstreamer_elements():
        sys.exit(1)
    
    print("\nStarting pipeline test...")
    print("Open VLC and use: Media -> Open Network Stream -> udp://@:5000")
    print("Or run: vlc udp://@:5000")
    print("\nPress Ctrl+C to stop the test")
    
    try:
        test_gstreamer_pipeline()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test failed: {e}")
