#!/usr/bin/env python3
"""
Test script for GStreamer IR streaming functionality
"""

import sys
import cv2
import numpy as np
import gi
gi.require_version('Gst', '1.0')
from gi.repository import Gst
import time
import os
from datetime import datetime

def save_frame_as_jpg(frame, output_dir="captured_frames", prefix="frame"):
    """Save a frame as JPG file with timestamp"""
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Generate filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # milliseconds
    filename = f"{prefix}_{timestamp}.jpg"
    filepath = os.path.join(output_dir, filename)

    # Save frame as JPG
    success = cv2.imwrite(filepath, frame)
    if success:
        print(f"Frame saved: {filepath}")
        return filepath
    else:
        print(f"Failed to save frame: {filepath}")
        return None

def capture_and_save_frame(source=0, output_dir="captured_frames", prefix="captured"):
    """Capture a frame from camera/video source and save as JPG

    Args:
        source: Camera index (0 for default camera) or video file path
        output_dir: Directory to save captured frames
        prefix: Prefix for saved filenames

    Returns:
        str: Path to saved file, or None if failed
    """
    print(f"Attempting to capture frame from source: {source}")

    # Initialize video capture
    cap = cv2.VideoCapture(source)

    if not cap.isOpened():
        print(f"Error: Could not open video source {source}")
        return None

    try:
        # Read a frame
        ret, frame = cap.read()

        if not ret:
            print("Error: Could not read frame from source")
            return None

        print(f"Successfully captured frame: {frame.shape}")

        # Save the frame
        filepath = save_frame_as_jpg(frame, output_dir, prefix)
        return filepath

    finally:
        # Release the capture
        cap.release()

def test_gstreamer_pipeline():
    """Test the GStreamer pipeline independently"""
    print("Testing GStreamer pipeline...")
    
    # Initialize GStreamer
    Gst.init(None)
    
    # Create the same pipeline as in display.py
    pipeline_str = (
        "appsrc name=src is-live=true do-timestamp=true ! "
        "videoconvert ! "
        "video/x-raw,format=I420 ! "
        "x264enc tune=zerolatency bitrate=2000 speed-preset=ultrafast key-int-max=30 ! "
        "rtph264pay config-interval=1 pt=96 ! "
        "udpsink host=127.0.0.1 port=5000 sync=false"
    )
    
    try:
        pipeline = Gst.parse_launch(pipeline_str)
        appsrc = pipeline.get_by_name("src")
        
        # Configure appsrc for RGB frames
        caps_str = "video/x-raw,format=BGR,width=1920,height=1080,framerate=30/1"
        caps = Gst.Caps.from_string(caps_str)
        appsrc.set_property("caps", caps)
        appsrc.set_property("stream-type", 0)
        appsrc.set_property("format", Gst.Format.TIME)
        appsrc.set_property("is-live", True)
        appsrc.set_property("do-timestamp", True)
        
        # Start pipeline
        ret = pipeline.set_state(Gst.State.PLAYING)
        if ret == Gst.StateChangeReturn.FAILURE:
            print("Failed to start pipeline")
            return False
        
        print("Pipeline started successfully!")
        print("VLC command: vlc udp://@:5000")
        print("Sending test frames for 30 seconds...")
        
        # Send test frames
        frame_count = 0
        start_time = time.time()

        while time.time() - start_time < 30:  # Run for 30 seconds
            # Create a test IR frame with moving pattern (grayscale)
            ir_frame = np.zeros((1080, 1920), dtype=np.uint8)

            # Create a moving vertical bar
            bar_pos = int((frame_count % 100) * 19.2)  # Move across screen
            ir_frame[:, bar_pos:bar_pos+50] = 255

            # Add frame counter text pattern
            cv2.putText(ir_frame, f"Frame {frame_count}", (50, 50),
                       cv2.FONT_HERSHEY_SIMPLEX, 2, 255, 3)

            # Convert IR frame to RGB using colormap (simulating thermal visualization)
            normalized_frame = cv2.normalize(ir_frame, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
            rgb_frame = cv2.applyColorMap(normalized_frame, cv2.COLORMAP_JET)

            # Save frame every 30 frames (once per second at 30fps)
            if frame_count % 30 == 0:
                save_frame_as_jpg(rgb_frame, "captured_frames", "test_frame")

            # Convert RGB frame to bytes
            frame_data = rgb_frame.tobytes()
            
            # Create buffer
            buffer = Gst.Buffer.new_allocate(None, len(frame_data), None)
            buffer.fill(0, frame_data)
            
            # Set timestamp
            timestamp = pipeline.get_clock().get_time() - pipeline.get_base_time()
            buffer.pts = timestamp
            buffer.dts = timestamp
            
            # Push buffer
            ret = appsrc.emit("push-buffer", buffer)
            if ret != Gst.FlowReturn.OK:
                print(f"Error pushing buffer: {ret}")
            else:
                if frame_count % 30 == 0:  # Print every 30 frames (1 second at 30fps)
                    print(f"Sent frame {frame_count}")
            
            frame_count += 1
            time.sleep(1/30)  # 30 FPS
        
        # Cleanup
        pipeline.set_state(Gst.State.NULL)
        print("Test completed!")
        return True
        
    except Exception as e:
        print(f"Error in test: {e}")
        return False

def check_gstreamer_elements():
    """Check if required GStreamer elements are available"""
    print("Checking GStreamer elements...")
    
    Gst.init(None)
    
    required_elements = [
        'appsrc', 'videoconvert', 'x264enc', 'rtph264pay', 'udpsink'
    ]
    
    missing_elements = []
    for element in required_elements:
        factory = Gst.ElementFactory.find(element)
        if factory is None:
            missing_elements.append(element)
        else:
            print(f"✓ {element} - available")
    
    if missing_elements:
        print(f"✗ Missing elements: {missing_elements}")
        print("Install missing GStreamer plugins:")
        print("sudo apt-get install gstreamer1.0-plugins-base gstreamer1.0-plugins-good gstreamer1.0-plugins-bad gstreamer1.0-plugins-ugly gstreamer1.0-libav")
        return False
    else:
        print("All required GStreamer elements are available!")
        return True

def main():
    """Main function with command line options"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "capture":
            # Capture a single frame from camera
            source = int(sys.argv[2]) if len(sys.argv) > 2 else 0
            print(f"Capturing frame from camera {source}...")
            filepath = capture_and_save_frame(source)
            if filepath:
                print(f"Frame successfully saved to: {filepath}")
            else:
                print("Failed to capture frame")
            return

        elif command == "help":
            print("Usage:")
            print("  python test_gstreamer.py                    - Run GStreamer pipeline test")
            print("  python test_gstreamer.py capture [camera]   - Capture single frame from camera")
            print("  python test_gstreamer.py help               - Show this help")
            print("\nExamples:")
            print("  python test_gstreamer.py capture            - Capture from default camera (0)")
            print("  python test_gstreamer.py capture 1          - Capture from camera 1")
            return

    # Default behavior - run GStreamer test
    print("GStreamer IR Streaming Test")
    print("=" * 40)

    # Check elements first
    if not check_gstreamer_elements():
        sys.exit(1)

    print("\nStarting pipeline test...")
    print("Open VLC and use: Media -> Open Network Stream -> udp://@:5000")
    print("Or run: vlc udp://@:5000")
    print("\nPress Ctrl+C to stop the test")

    try:
        test_gstreamer_pipeline()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test failed: {e}")

if __name__ == "__main__":
    main()
