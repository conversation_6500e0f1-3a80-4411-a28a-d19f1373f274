#!/usr/bin/env python3
"""
Test script for IPC IR frame communication
"""

import sys
import cv2
import numpy as np
import time
import os
from datetime import datetime
from display import Display

def create_test_ir_frame(width=1920, height=1080, pattern_type="gradient", frame_number=0):
    """Create a test IR frame with different patterns"""
    
    if pattern_type == "gradient":
        # Create a thermal gradient pattern
        frame = np.zeros((height, width), dtype=np.uint8)
        for i in range(height):
            frame[i, :] = int((i / height) * 255)
    
    elif pattern_type == "circular":
        # Create a circular thermal pattern (hot center)
        frame = np.zeros((height, width), dtype=np.uint8)
        center_x, center_y = width // 2, height // 2
        max_distance = min(width, height) // 2
        
        for y in range(height):
            for x in range(width):
                distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                if distance <= max_distance:
                    intensity = int(255 * (1 - distance / max_distance))
                    frame[y, x] = intensity
    
    elif pattern_type == "moving_bar":
        # Create a moving vertical bar
        frame = np.zeros((height, width), dtype=np.uint8)
        bar_pos = int((frame_number % 100) * (width / 100))
        frame[:, bar_pos:bar_pos+50] = 255
        
        # Add frame counter
        cv2.putText(frame, f"Frame {frame_number}", (50, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, 128, 3)
    
    elif pattern_type == "random":
        # Create random thermal noise pattern
        frame = np.random.randint(0, 256, (height, width), dtype=np.uint8)
        # Apply some smoothing to make it look more realistic
        frame = cv2.GaussianBlur(frame, (15, 15), 0)
    
    else:  # default to gradient
        frame = np.zeros((height, width), dtype=np.uint8)
        for i in range(height):
            frame[i, :] = int((i / height) * 255)
    
    return frame

def test_single_frame():
    """Test sending a single IR frame via IPC"""
    print("Testing single IR frame transmission via IPC...")
    
    # Initialize IPC
    if not Display.init_ipc():
        print("Failed to initialize IPC")
        return False
    
    # Create test frame
    test_frame = create_test_ir_frame(640, 480, "circular")
    
    # Send frame
    Display.send_ir_frame_ipc(test_frame)
    
    print("Single frame sent. Check with ipc_receiver.py")
    return True

def test_continuous_frames(duration=30, fps=10, pattern="moving_bar"):
    """Test sending continuous IR frames via IPC"""
    print(f"Testing continuous IR frame transmission for {duration} seconds at {fps} FPS...")
    print(f"Pattern: {pattern}")
    
    # Initialize IPC
    if not Display.init_ipc():
        print("Failed to initialize IPC")
        return False
    
    frame_count = 0
    start_time = time.time()
    frame_interval = 1.0 / fps
    
    try:
        while time.time() - start_time < duration:
            # Create test frame
            test_frame = create_test_ir_frame(640, 480, pattern, frame_count)
            
            # Send frame
            Display.send_ir_frame_ipc(test_frame)
            
            frame_count += 1
            
            # Print progress every second
            if frame_count % fps == 0:
                elapsed = time.time() - start_time
                print(f"Sent {frame_count} frames in {elapsed:.1f}s")
            
            # Wait for next frame
            time.sleep(frame_interval)
            
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    
    print(f"Test completed. Sent {frame_count} frames.")
    return True

def test_different_patterns():
    """Test different IR frame patterns"""
    patterns = ["gradient", "circular", "moving_bar", "random"]
    
    print("Testing different IR frame patterns...")
    
    # Initialize IPC
    if not Display.init_ipc():
        print("Failed to initialize IPC")
        return False
    
    for i, pattern in enumerate(patterns):
        print(f"Sending {pattern} pattern...")
        
        # Create and send test frame
        test_frame = create_test_ir_frame(640, 480, pattern, i)
        Display.send_ir_frame_ipc(test_frame)
        
        # Wait between patterns
        time.sleep(2)
    
    print("Pattern test completed.")
    return True

def benchmark_ipc():
    """Benchmark IPC frame transmission performance"""
    print("Benchmarking IPC frame transmission...")
    
    # Initialize IPC
    if not Display.init_ipc():
        print("Failed to initialize IPC")
        return False
    
    # Test different frame sizes
    sizes = [(320, 240), (640, 480), (1280, 720), (1920, 1080)]
    
    for width, height in sizes:
        print(f"\nTesting {width}x{height} frames...")
        
        # Create test frame
        test_frame = create_test_ir_frame(width, height, "gradient")
        
        # Benchmark transmission
        num_frames = 100
        start_time = time.time()
        
        for i in range(num_frames):
            Display.send_ir_frame_ipc(test_frame)
        
        end_time = time.time()
        duration = end_time - start_time
        fps = num_frames / duration
        frame_size = width * height * 3  # RGB
        throughput = (frame_size * num_frames) / (1024 * 1024 * duration)  # MB/s
        
        print(f"  Sent {num_frames} frames in {duration:.2f}s")
        print(f"  Average FPS: {fps:.1f}")
        print(f"  Frame size: {frame_size} bytes")
        print(f"  Throughput: {throughput:.1f} MB/s")
    
    return True

def main():
    """Main function with command line interface"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "single":
            # Test single frame
            test_single_frame()
            
        elif command == "continuous":
            # Test continuous frames
            duration = int(sys.argv[2]) if len(sys.argv) > 2 else 30
            fps = int(sys.argv[3]) if len(sys.argv) > 3 else 10
            pattern = sys.argv[4] if len(sys.argv) > 4 else "moving_bar"
            test_continuous_frames(duration, fps, pattern)
            
        elif command == "patterns":
            # Test different patterns
            test_different_patterns()
            
        elif command == "benchmark":
            # Benchmark performance
            benchmark_ipc()
            
        elif command == "help":
            print("Usage:")
            print("  python test_ipc.py single                           - Send single frame")
            print("  python test_ipc.py continuous [duration] [fps] [pattern] - Send continuous frames")
            print("  python test_ipc.py patterns                         - Test different patterns")
            print("  python test_ipc.py benchmark                        - Benchmark performance")
            print("  python test_ipc.py help                             - Show this help")
            print("\nExamples:")
            print("  python test_ipc.py single")
            print("  python test_ipc.py continuous 60 30 moving_bar")
            print("  python test_ipc.py patterns")
            print("\nPatterns: gradient, circular, moving_bar, random")
            print("\nNote: Run 'python ipc_receiver.py monitor' in another terminal to view frames")
            
    else:
        # Default: test single frame
        print("Default: Testing single frame transmission")
        print("Run 'python test_ipc.py help' for more options")
        test_single_frame()

if __name__ == "__main__":
    main()
