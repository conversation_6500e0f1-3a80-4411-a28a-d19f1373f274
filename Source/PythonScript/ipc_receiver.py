#!/usr/bin/env python3
"""
IPC Receiver for IR frames
This script demonstrates how to receive IR frames sent via IPC from the display module
"""

import sys
import cv2
import numpy as np
import multiprocessing as mp
import time
import os
from datetime import datetime

class IPCReceiver:
    def __init__(self, shared_memory_name="ir_frame_buffer", max_frame_size=1920*1080*3):
        self.shared_memory_name = shared_memory_name
        self.max_frame_size = max_frame_size
        self.shared_memory = None
        self.frame_queue = None
        self.frame_metadata_queue = None
        self.running = False
        
    def connect(self):
        """Connect to the shared memory and queues"""
        try:
            # Connect to existing shared memory
            self.shared_memory = mp.shared_memory.SharedMemory(
                name=self.shared_memory_name, 
                create=False
            )
            
            # Create queues (these will be separate from the sender's queues)
            # In a real implementation, you might use named pipes or other IPC mechanisms
            print(f"Connected to shared memory: {self.shared_memory_name}")
            print(f"Shared memory size: {len(self.shared_memory.buf)} bytes")
            return True
            
        except FileNotFoundError:
            print(f"Shared memory '{self.shared_memory_name}' not found. Make sure the sender is running.")
            return False
        except Exception as e:
            print(f"Error connecting to shared memory: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from shared memory"""
        try:
            if self.shared_memory:
                self.shared_memory.close()
                print("Disconnected from shared memory")
        except Exception as e:
            print(f"Error disconnecting: {e}")
    
    def read_frame_from_memory(self, width=1920, height=1080, channels=3):
        """Read frame data from shared memory"""
        try:
            frame_size = width * height * channels
            if frame_size > len(self.shared_memory.buf):
                print(f"Frame size {frame_size} exceeds shared memory size {len(self.shared_memory.buf)}")
                return None
            
            # Read frame data from shared memory
            frame_data = bytes(self.shared_memory.buf[:frame_size])
            
            # Convert bytes back to numpy array
            frame = np.frombuffer(frame_data, dtype=np.uint8)
            frame = frame.reshape((height, width, channels))
            
            return frame
            
        except Exception as e:
            print(f"Error reading frame from memory: {e}")
            return None
    
    def save_frame(self, frame, output_dir="received_frames", prefix="received"):
        """Save received frame as JPG"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        filename = f"{prefix}_{timestamp}.jpg"
        filepath = os.path.join(output_dir, filename)
        
        success = cv2.imwrite(filepath, frame)
        if success:
            print(f"Frame saved: {filepath}")
            return filepath
        else:
            print(f"Failed to save frame: {filepath}")
            return None
    
    def monitor_frames(self, display=True, save_frames=False, save_interval=30):
        """Monitor and display/save received frames"""
        if not self.connect():
            return
        
        self.running = True
        frame_count = 0
        last_save_time = time.time()
        
        print("Monitoring IR frames via IPC...")
        print("Press 'q' to quit, 's' to save current frame")
        
        try:
            while self.running:
                # Read frame from shared memory
                frame = self.read_frame_from_memory()
                
                if frame is not None:
                    frame_count += 1
                    
                    if display:
                        # Add frame info overlay
                        info_text = f"Frame: {frame_count} | Size: {frame.shape} | Time: {time.strftime('%H:%M:%S')}"
                        cv2.putText(frame, info_text, (10, 30), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        
                        # Display frame
                        cv2.imshow('IPC IR Frame Receiver', frame)
                        
                        # Handle key presses
                        key = cv2.waitKey(1) & 0xFF
                        if key == ord('q'):
                            break
                        elif key == ord('s'):
                            self.save_frame(frame, "received_frames", "manual_save")
                    
                    # Auto-save frames at intervals
                    if save_frames and (time.time() - last_save_time) >= save_interval:
                        self.save_frame(frame, "received_frames", "auto_save")
                        last_save_time = time.time()
                
                # Small delay to prevent excessive CPU usage
                time.sleep(0.033)  # ~30 FPS
                
        except KeyboardInterrupt:
            print("\nReceiver interrupted by user")
        finally:
            self.running = False
            if display:
                cv2.destroyAllWindows()
            self.disconnect()
    
    def capture_single_frame(self):
        """Capture and save a single frame"""
        if not self.connect():
            return None
        
        try:
            frame = self.read_frame_from_memory()
            if frame is not None:
                filepath = self.save_frame(frame, "received_frames", "single_capture")
                print(f"Single frame captured and saved: {filepath}")
                return filepath
            else:
                print("No frame data available")
                return None
        finally:
            self.disconnect()

def main():
    """Main function with command line interface"""
    receiver = IPCReceiver()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "monitor":
            # Monitor and display frames
            save_frames = "--save" in sys.argv
            receiver.monitor_frames(display=True, save_frames=save_frames)
            
        elif command == "capture":
            # Capture single frame
            receiver.capture_single_frame()
            
        elif command == "save":
            # Monitor and save frames without display
            receiver.monitor_frames(display=False, save_frames=True, save_interval=1)
            
        elif command == "test":
            # Test connection
            if receiver.connect():
                print("Successfully connected to IPC shared memory")
                frame = receiver.read_frame_from_memory()
                if frame is not None:
                    print(f"Successfully read frame: {frame.shape}")
                else:
                    print("No frame data available")
                receiver.disconnect()
            else:
                print("Failed to connect to IPC shared memory")
                
        elif command == "help":
            print("Usage:")
            print("  python ipc_receiver.py monitor [--save]  - Monitor and display frames")
            print("  python ipc_receiver.py capture           - Capture single frame")
            print("  python ipc_receiver.py save              - Save frames without display")
            print("  python ipc_receiver.py test              - Test IPC connection")
            print("  python ipc_receiver.py help              - Show this help")
            print("\nExamples:")
            print("  python ipc_receiver.py monitor           - Display frames in real-time")
            print("  python ipc_receiver.py monitor --save    - Display and auto-save frames")
            print("  python ipc_receiver.py capture           - Capture and save one frame")
            
    else:
        # Default: monitor frames
        receiver.monitor_frames(display=True, save_frames=False)

if __name__ == "__main__":
    main()
