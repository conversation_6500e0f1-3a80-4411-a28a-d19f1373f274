#!/usr/bin/env python3
"""
Test script for IR to RGB conversion functionality
"""

import sys
import cv2
import numpy as np
import os
from datetime import datetime

def create_test_ir_frame(width=1920, height=1080, pattern_type="gradient"):
    """Create a test IR frame with different patterns"""
    
    if pattern_type == "gradient":
        # Create a thermal gradient pattern
        frame = np.zeros((height, width), dtype=np.uint8)
        for i in range(height):
            frame[i, :] = int((i / height) * 255)
    
    elif pattern_type == "circular":
        # Create a circular thermal pattern (hot center)
        frame = np.zeros((height, width), dtype=np.uint8)
        center_x, center_y = width // 2, height // 2
        max_distance = min(width, height) // 2
        
        for y in range(height):
            for x in range(width):
                distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                if distance <= max_distance:
                    intensity = int(255 * (1 - distance / max_distance))
                    frame[y, x] = intensity
    
    elif pattern_type == "random":
        # Create random thermal noise pattern
        frame = np.random.randint(0, 256, (height, width), dtype=np.uint8)
        # Apply some smoothing to make it look more realistic
        frame = cv2.GaussianBlur(frame, (15, 15), 0)
    
    else:  # default to gradient
        frame = np.zeros((height, width), dtype=np.uint8)
        for i in range(height):
            frame[i, :] = int((i / height) * 255)
    
    return frame

def convert_ir_to_rgb(ir_frame, colormap=cv2.COLORMAP_JET):
    """Convert IR frame to RGB using colormap"""
    try:
        # Ensure frame is grayscale
        if len(ir_frame.shape) == 3:
            ir_frame = cv2.cvtColor(ir_frame, cv2.COLOR_BGR2GRAY)
        
        # Normalize IR frame to 0-255 range for better colormap application
        normalized_frame = cv2.normalize(ir_frame, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        
        # Apply colormap to convert grayscale to RGB
        rgb_frame = cv2.applyColorMap(normalized_frame, colormap)
        
        return rgb_frame
        
    except Exception as e:
        print(f"Error converting IR to RGB: {e}")
        # Fallback: convert grayscale to 3-channel BGR
        if len(ir_frame.shape) == 2:
            return cv2.cvtColor(ir_frame, cv2.COLOR_GRAY2BGR)
        return ir_frame

def save_frame_as_jpg(frame, output_dir="test_output", filename=None):
    """Save a frame as JPG file"""
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Generate filename if not provided
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        filename = f"frame_{timestamp}.jpg"
    
    filepath = os.path.join(output_dir, filename)
    
    # Save frame as JPG
    success = cv2.imwrite(filepath, frame)
    if success:
        print(f"Frame saved: {filepath}")
        return filepath
    else:
        print(f"Failed to save frame: {filepath}")
        return None

def test_colormaps():
    """Test different colormaps with IR frames"""
    
    # Available colormaps
    colormaps = {
        'JET': cv2.COLORMAP_JET,
        'HOT': cv2.COLORMAP_HOT,
        'COOL': cv2.COLORMAP_COOL,
        'SPRING': cv2.COLORMAP_SPRING,
        'SUMMER': cv2.COLORMAP_SUMMER,
        'AUTUMN': cv2.COLORMAP_AUTUMN,
        'WINTER': cv2.COLORMAP_WINTER,
        'BONE': cv2.COLORMAP_BONE,
        'PINK': cv2.COLORMAP_PINK,
        'HSV': cv2.COLORMAP_HSV,
        'PARULA': cv2.COLORMAP_PARULA,
        'MAGMA': cv2.COLORMAP_MAGMA,
        'INFERNO': cv2.COLORMAP_INFERNO,
        'PLASMA': cv2.COLORMAP_PLASMA,
        'VIRIDIS': cv2.COLORMAP_VIRIDIS,
        'TURBO': cv2.COLORMAP_TURBO
    }
    
    # Test patterns
    patterns = ["gradient", "circular", "random"]
    
    print("Testing IR to RGB conversion with different colormaps and patterns...")
    
    for pattern in patterns:
        print(f"\nTesting pattern: {pattern}")
        
        # Create test IR frame
        ir_frame = create_test_ir_frame(640, 480, pattern)
        
        # Save original IR frame
        save_frame_as_jpg(ir_frame, "test_output", f"ir_original_{pattern}.jpg")
        
        # Test each colormap
        for name, colormap in colormaps.items():
            print(f"  Converting with colormap: {name}")
            
            # Convert IR to RGB
            rgb_frame = convert_ir_to_rgb(ir_frame, colormap)
            
            # Save RGB frame
            save_frame_as_jpg(rgb_frame, "test_output", f"rgb_{pattern}_{name.lower()}.jpg")

def main():
    """Main function"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "test":
            # Run colormap tests
            test_colormaps()
            print("\nTest completed! Check the 'test_output' directory for results.")
            
        elif command == "demo":
            # Interactive demo
            print("IR to RGB Conversion Demo")
            print("Press 'q' to quit, 'c' to change colormap, 's' to save frame")
            
            # Create a test IR frame
            ir_frame = create_test_ir_frame(640, 480, "circular")
            
            # Available colormaps
            colormaps = [
                ('JET', cv2.COLORMAP_JET),
                ('HOT', cv2.COLORMAP_HOT),
                ('COOL', cv2.COLORMAP_COOL),
                ('VIRIDIS', cv2.COLORMAP_VIRIDIS),
                ('PLASMA', cv2.COLORMAP_PLASMA),
                ('INFERNO', cv2.COLORMAP_INFERNO),
                ('MAGMA', cv2.COLORMAP_MAGMA),
                ('TURBO', cv2.COLORMAP_TURBO)
            ]
            
            current_colormap_idx = 0
            
            while True:
                # Convert IR to RGB with current colormap
                name, colormap = colormaps[current_colormap_idx]
                rgb_frame = convert_ir_to_rgb(ir_frame, colormap)
                
                # Add text overlay
                cv2.putText(rgb_frame, f"Colormap: {name}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                cv2.putText(rgb_frame, "Press 'c' to change, 's' to save, 'q' to quit", (10, 460), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
                
                # Display frame
                cv2.imshow('IR to RGB Demo', rgb_frame)
                
                # Handle key presses
                key = cv2.waitKey(30) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('c'):
                    current_colormap_idx = (current_colormap_idx + 1) % len(colormaps)
                    print(f"Changed to colormap: {colormaps[current_colormap_idx][0]}")
                elif key == ord('s'):
                    save_frame_as_jpg(rgb_frame, "test_output", f"demo_{name.lower()}.jpg")
            
            cv2.destroyAllWindows()
            
        elif command == "help":
            print("Usage:")
            print("  python test_ir_to_rgb.py test    - Run automated colormap tests")
            print("  python test_ir_to_rgb.py demo    - Run interactive demo")
            print("  python test_ir_to_rgb.py help    - Show this help")
            
    else:
        # Default: run tests
        test_colormaps()
        print("\nTest completed! Check the 'test_output' directory for results.")
        print("Run 'python test_ir_to_rgb.py help' for more options.")

if __name__ == "__main__":
    main()
